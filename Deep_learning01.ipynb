{"cells": [{"cell_type": "code", "execution_count": null, "id": "3c8f47b0", "metadata": {}, "outputs": [], "source": ["import numpy as np \n", "import pandas as pd \n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import cv2\n", "import tensorflow as tf\n", "from PIL import Image\n", "import os\n", "from sklearn.model_selection import train_test_split\n", "from keras.utils import to_categorical\n", "from keras.models import Sequential, load_model\n", "from keras.layers import Conv2D, MaxPool2D, Dense, Flatten, Dropout\n", "\n", "data = []\n", "labels = []\n", "classes = 43\n", "dataset_dir = 'C:\\Users\\<USER>\\Downloads\\dataset_ifys'\n", "#Retrieving the images and their labels \n", "metaDf = pd.read_csv('C:\\Users\\<USER>\\Downloads\\dataset_ifys\\Meta.csv')\n", "trainDf = pd.read_csv('C:\\Users\\<USER>\\Downloads\\dataset_ifys\\Train.csv') \n", "testDf= pd.read_csv('C:\\Users\\<USER>\\Downloads\\dataset_ifys\\Test.csv') \n", "labels = ['20 km/h', '30 km/h', '50 km/h', '60 km/h', '70 km/h', '80 km/h', '80 km/h end', '100 km/h', '120 km/h', 'No overtaking',\n", "               'No overtaking for tracks', 'Crossroad with secondary way', 'Main road', 'Give way', 'Stop', 'Road up', 'Road up for track', 'Brock',\n", "               'Other dangerous', 'Turn left', 'Turn right', 'Winding road', 'Hollow road', 'Slippery road', 'Narrowing road', 'Roadwork', 'Traffic light',\n", "               'Pedestrian', 'Children', 'Bike', 'Snow', 'Deer', 'End of the limits', 'Only right', 'Only left', 'Only straight', 'Only straight and right', \n", "               'Only straight and left', 'Take right', 'Take left', 'Circle crossroad', 'End of overtaking limit', 'End of overtaking limit for track']\n", "print('SHAPE of training set:',trainDf.shape)\n", "print('SHAPE of test set:',trainDf.shape)\n", "print('SHAPE of MetaInfo:',trainDf.shape)\n", "\n", "trainDf['Path'] = list(map(lambda x: os.path.join(dataset_dir,x.lower()), trainDf['Path']))\n", "testDf['Path'] = list(map(lambda x: os.path.join(dataset_dir,x.lower()), testDf['Path']))\n", "metaDf['Path'] = list(map(lambda x: os.path.join(dataset_dir,x.lower()), metaDf['Path']))\n", "\n", "trainDf.sample(10)\n", "\n", "fig, axs = plt.subplots(1, 2, sharex=True, sharey=True, figsize=(25, 6))\n", "axs[0].set_title('Train classes distribution')\n", "axs[0].set_xlabel('Class')\n", "axs[0].set_ylabel('Count')\n", "axs[1].set_title('Test classes distribution')\n", "axs[1].set_xlabel('Class')\n", "axs[1].set_ylabel('Count')\n", "sns.countplot(trainDf.ClassId, ax=axs[0],palette = \"Set1\")\n", "sns.countplot(testDf.ClassId, ax=axs[1])\n", "axs[0].set_xlabel('Class ID');\n", "axs[1].set_xlabel('Class ID');\n", "\n", "trainDfDpiSubset = trainDf[(trainDf.Width < 80) & (trainDf.Height < 80)];\n", "testDfDpiSubset = testDf[(testDf.Width < 80) & (testDf.Height < 80)];\n", "g = sns.JointGrid(x=\"Width\", y=\"Height\", data=trainDfDpiSubset)\n", "sns.kdeplot(trainDfDpiSubset.Width, trainDfDpiSubset.Height, cmap=\"Reds\",shade=False, shade_lowest=False, ax=g.ax_joint)\n", "sns.kdeplot(testDfDpiSubset.Width, testDfDpiSubset.Height, cmap=\"Blues\", shade=False, shade_lowest=False, ax=g.ax_joint)\n", "g.fig.set_figwidth(25)\n", "g.fig.set_figheight(8)\n", "plt.show();\n", "\n", "sns.set_style()\n", "rows = 6\n", "cols = 8\n", "fig, axs = plt.subplots(rows, cols, sharex=True, sharey=True, figsize=(25, 12))\n", "plt.subplots_adjust(left=None, bottom=None, right=None, top=0.9, wspace=None, hspace=None)\n", "metaDf = metaDf.sort_values(by=['ClassId'])\n", "idx = 0\n", "for i in range(rows):\n", "    for j in range(cols):\n", "        if idx > 42:\n", "            break\n", "            \n", "        img = cv2.imread(metaDf[\"Path\"].tolist()[idx], cv2.IMREAD_UNCHANGED)\n", "        img[np.where(img[:,:,3]==0)] = [255,255,255,255]\n", "        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        img = cv2.resize(img, (60,60))\n", "        \n", "        axs[i,j].imshow(img)\n", "        axs[i,j].set_facecolor('xkcd:salmon')\n", "        axs[i,j].set_facecolor((1.0, 0.47, 0.42))\n", "        axs[i,j].set_title(labels[int(metaDf[\"ClassId\"].tolist()[idx])])\n", "        axs[i,j].get_xaxis().set_visible(False)\n", "        axs[i,j].get_yaxis().set_visible(False)\n", "        idx += 1\n", "\n", "rows = 10\n", "cols = 10\n", "fig, axs = plt.subplots(rows, cols, sharex=True, sharey=True, figsize=(25, 12))\n", "plt.subplots_adjust(left=None, bottom=None, right=None, top=0.9, wspace=None, hspace=None)\n", "cur_path = 'C:/Users/<USER>/Documents/Traffic_sign_Recognition/'\n", "print(cur_path)\n", "idx = 0\n", "for i in range(rows):\n", "    for j in range(cols):\n", "        path = os.path.join(cur_path,trainDf[\"Path\"].tolist()[idx]) \n", "        img = cv2.imread(path,cv2.IMREAD_UNCHANGED)\n", "        print(path)\n", "        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n", "        img = cv2.resize(img, (60,60))\n", "        axs[i,j].imshow(img)\n", "        axs[i,j].set_title(labels[int(trainDf[\"ClassId\"].tolist()[idx])])\n", "        axs[i,j].get_xaxis().set_visible(False)\n", "        axs[i,j].get_yaxis().set_visible(False)\n", "        idx += 1\n", "\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}