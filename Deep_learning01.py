import numpy as np 
import pandas as pd 
import matplotlib.pyplot as plt
import seaborn as sns
import cv2
import tensorflow as tf
from PIL import Image
import os
from sklearn.model_selection import train_test_split
from keras.utils import to_categorical
from keras.models import Sequential, load_model
from keras.layers import Conv2D, MaxPool2D, Dense, Flatten, Dropout

data = []
labels = []
classes = 43
dataset_dir = 'D:\\Deep_learning projects\\Traffic_sign_Recognition'
#Retrieving the images and their labels 
metaDf = pd.read_csv('D:\\Deep_learning projects\\Traffic_sign_Recognition\\Meta.csv') 
trainDf = pd.read_csv('D:\\Deep_learning projects\\Traffic_sign_Recognition\\Train.csv') 
testDf= pd.read_csv('D:\\Deep_learning projects\\Traffic_sign_Recognition\\Test.csv') 
labels = ['20 km/h', '30 km/h', '50 km/h', '60 km/h', '70 km/h', '80 km/h', '80 km/h end', '100 km/h', '120 km/h', 'No overtaking',
               'No overtaking for tracks', 'Crossroad with secondary way', 'Main road', 'Give way', 'Stop', 'Road up', 'Road up for track', 'Brock',
               'Other dangerous', 'Turn left', 'Turn right', 'Winding road', 'Hollow road', 'Slippery road', 'Narrowing road', 'Roadwork', 'Traffic light',
               'Pedestrian', 'Children', 'Bike', 'Snow', 'Deer', 'End of the limits', 'Only right', 'Only left', 'Only straight', 'Only straight and right', 
               'Only straight and left', 'Take right', 'Take left', 'Circle crossroad', 'End of overtaking limit', 'End of overtaking limit for track']
print('SHAPE of training set:',trainDf.shape)
print('SHAPE of test set:',trainDf.shape)
print('SHAPE of MetaInfo:',trainDf.shape)

trainDf['Path'] = list(map(lambda x: os.path.join(dataset_dir,x.lower()), trainDf['Path']))
testDf['Path'] = list(map(lambda x: os.path.join(dataset_dir,x.lower()), testDf['Path']))
metaDf['Path'] = list(map(lambda x: os.path.join(dataset_dir,x.lower()), metaDf['Path']))

print(trainDf.sample(10))

fig, axs = plt.subplots(1, 2, sharex=True, sharey=True, figsize=(25, 6))
axs[0].set_title('Train classes distribution')
axs[0].set_xlabel('Class')
axs[0].set_ylabel('Count')
axs[1].set_title('Test classes distribution')
axs[1].set_xlabel('Class')
axs[1].set_ylabel('Count')
sns.countplot(x='ClassId', data=trainDf, ax=axs[0], palette="Set1")
sns.countplot(x='ClassId', data=testDf, ax=axs[1])
axs[0].set_xlabel('Class ID')
axs[1].set_xlabel('Class ID')

trainDfDpiSubset = trainDf[(trainDf.Width < 80) & (trainDf.Height < 80)]
testDfDpiSubset = testDf[(testDf.Width < 80) & (testDf.Height < 80)]
g = sns.JointGrid(x="Width", y="Height", data=trainDfDpiSubset)
sns.kdeplot(x=trainDfDpiSubset.Width, y=trainDfDpiSubset.Height, cmap="Reds", fill=False, ax=g.ax_joint)
sns.kdeplot(x=testDfDpiSubset.Width, y=testDfDpiSubset.Height, cmap="Blues", fill=False, ax=g.ax_joint)
g.fig.set_figwidth(25)
g.fig.set_figheight(8)
plt.show()

sns.set_style()
rows = 6
cols = 8
fig, axs = plt.subplots(rows, cols, sharex=True, sharey=True, figsize=(25, 12))
plt.subplots_adjust(left=None, bottom=None, right=None, top=0.9, wspace=None, hspace=None)
metaDf = metaDf.sort_values(by=['ClassId'])
idx = 0
for i in range(rows):
    for j in range(cols):
        if idx > 42:
            break
            
        img = cv2.imread(metaDf["Path"].tolist()[idx], cv2.IMREAD_UNCHANGED)
        if img is not None:
            img[np.where(img[:,:,3]==0)] = [255,255,255,255]
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            img = cv2.resize(img, (60,60))
            
            axs[i,j].imshow(img)
            axs[i,j].set_facecolor('xkcd:salmon')
            axs[i,j].set_facecolor((1.0, 0.47, 0.42))
            axs[i,j].set_title(labels[int(metaDf["ClassId"].tolist()[idx])])
            axs[i,j].get_xaxis().set_visible(False)
            axs[i,j].get_yaxis().set_visible(False)
        idx += 1

rows = 10
cols = 10
fig, axs = plt.subplots(rows, cols, sharex=True, sharey=True, figsize=(25, 12))
plt.subplots_adjust(left=None, bottom=None, right=None, top=0.9, wspace=None, hspace=None)
cur_path = 'C:/Users/<USER>/Documents/Traffic_sign_Recognition/'
print(cur_path)
idx = 0
for i in range(rows):
    for j in range(cols):
        if idx < len(trainDf):
            path = os.path.join(cur_path,trainDf["Path"].tolist()[idx]) 
            img = cv2.imread(path,cv2.IMREAD_UNCHANGED)
            print(path)
            if img is not None:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
                img = cv2.resize(img, (60,60))
                axs[i,j].imshow(img)
                axs[i,j].set_title(labels[int(trainDf["ClassId"].tolist()[idx])])
                axs[i,j].get_xaxis().set_visible(False)
                axs[i,j].get_yaxis().set_visible(False)
        idx += 1

plt.show()
